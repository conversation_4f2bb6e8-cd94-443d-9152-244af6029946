import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rideoon/views/onboarding/onboarding_container.dart';
import 'package:rideoon/views/authentication/userauth/sign_in.dart';
import 'package:rideoon/views/authentication/userauth/sign_up.dart';
import 'package:rideoon/views/authentication/riderauth/sign_in.dart';

class OnboardingWrapper extends StatefulWidget {
  const OnboardingWrapper({super.key});

  @override
  State<OnboardingWrapper> createState() => _OnboardingWrapperState();
}

class _OnboardingWrapperState extends State<OnboardingWrapper> {
  bool _isLoading = true;
  bool _isFirstTime = true;
  bool _hasCompletedOnboarding = false;
  String? _userType;

  @override
  void initState() {
    super.initState();
    _checkFirstTimeUser();
  }

  Future<void> _checkFirstTimeUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFirstTime = prefs.getBool('is_first_time') ?? true;
      final hasCompletedOnboarding = prefs.getBool('has_completed_onboarding') ?? false;
      final userType = prefs.getString('user_type');

      setState(() {
        _isFirstTime = isFirstTime;
        _hasCompletedOnboarding = hasCompletedOnboarding;
        _userType = userType;
        _isLoading = false;
      });
    } catch (e) {
      // Handle error gracefully
      setState(() {
        _isFirstTime = true;
        _hasCompletedOnboarding = false;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // If it's the first time, show onboarding
    if (_isFirstTime) {
      return const OnboardingContainer();
    }

    // If user has completed onboarding, go directly to sign up (default client type)
    if (_hasCompletedOnboarding || _userType == 'client') {
      return const UserSignUp();
    }

    // For riders (if we ever need this in the future)
    if (_userType == 'rider') {
      return const RiderSignIn();
    }

    // Default fallback to onboarding
    return const OnboardingContainer();
  }
}

class OnboardingService {
  static const String _isFirstTimeKey = 'is_first_time';
  static const String _userTypeKey = 'user_type';
  static const String _hasCompletedOnboardingKey = 'has_completed_onboarding';

  /// Check if this is the first time the user is opening the app
  static Future<bool> isFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isFirstTimeKey) ?? true;
  }

  /// Mark that the user has seen the app before
  static Future<void> setNotFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isFirstTimeKey, false);
  }

  /// Save the user type (client or rider)
  static Future<void> setUserType(String userType) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userTypeKey, userType);
  }

  /// Get the saved user type
  static Future<String?> getUserType() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userTypeKey);
  }

  /// Mark that the user has completed onboarding
  static Future<void> setOnboardingCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasCompletedOnboardingKey, true);
    await setNotFirstTime();
  }

  /// Check if the user has completed onboarding
  static Future<bool> hasCompletedOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_hasCompletedOnboardingKey) ?? false;
  }

  /// Clear all onboarding data (useful for testing or logout)
  static Future<void> clearOnboardingData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_isFirstTimeKey);
    await prefs.remove(_userTypeKey);
    await prefs.remove(_hasCompletedOnboardingKey);
  }

  /// Reset to first time user (useful for testing)
  static Future<void> resetToFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isFirstTimeKey, true);
    await prefs.remove(_userTypeKey);
    await prefs.remove(_hasCompletedOnboardingKey);
  }

  /// Mark that user has completed sign up (for future navigation to sign in)
  static Future<void> setSignUpCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('has_signed_up', true);
  }

  /// Check if user has completed sign up
  static Future<bool> hasSignedUp() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('has_signed_up') ?? false;
  }
}