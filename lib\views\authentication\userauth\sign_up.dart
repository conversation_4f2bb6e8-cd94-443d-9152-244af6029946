import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/authentication/userauth/sign_in.dart';
import 'package:rideoon/views/authentication/userauth/account_verification.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/auth/sign_up_request.dart';
import 'package:rideoon/providers/toast_provider.dart';

class UserSignUp extends StatefulWidget {
  const UserSignUp({super.key});

  @override
  State<UserSignUp> createState() => _UserSignUpState();
}

class _UserSignUpState extends State<UserSignUp> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _agreeToTerms = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
    });
  }

  void _toggleTermsAgreement() {
    setState(() {
      _agreeToTerms = !_agreeToTerms;
    });
  }

  void _navigateToSignIn() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const UserSignIn(),
      ),
    );
  }

  void _navigateToVerification() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => UserAccountVerification(
          email: _emailController.text.trim(),
          phoneNumber: _phoneController.text.trim(),
          firstName: _firstNameController.text.trim(),
        ),
      ),
    );
  }

  /// Convert API error messages to user-friendly messages
  String _getUserFriendlyErrorMessage(String apiErrorMessage) {
    final message = apiErrorMessage.toLowerCase();

    // Password validation errors
    if (message.contains('password should contain at least 1 special character')) {
      return 'Your password needs at least one special character like @, #, \$, %, etc.';
    }
    if (message.contains('password should contain at least 1 uppercase letter')) {
      return 'Your password needs at least one uppercase letter (A-Z).';
    }
    if (message.contains('password should contain at least 1 lowercase letter')) {
      return 'Your password needs at least one lowercase letter (a-z).';
    }
    if (message.contains('password should contain at least 1 number')) {
      return 'Your password needs at least one number (0-9).';
    }
    if (message.contains('password must be at least') || message.contains('password should be at least')) {
      return 'Your password must be at least 8 characters long.';
    }
    if (message.contains('passwords do not match') || message.contains('password mismatch')) {
      return 'The passwords you entered don\'t match. Please try again.';
    }

    // Email validation errors
    if (message.contains('invalid email') || message.contains('email format')) {
      return 'Please enter a valid email address.';
    }
    if (message.contains('email already exists') || message.contains('email is already registered')) {
      return 'This email is already registered. Try signing in instead.';
    }

    // Phone number validation errors
    if (message.contains('invalid phone') || message.contains('phone number format')) {
      return 'Please enter a valid phone number.';
    }
    if (message.contains('phone number already exists') || message.contains('phone is already registered')) {
      return 'This phone number is already registered. Try signing in instead.';
    }

    // Name validation errors
    if (message.contains('first name') && (message.contains('required') || message.contains('empty'))) {
      return 'Please enter your first name.';
    }
    if (message.contains('last name') && (message.contains('required') || message.contains('empty'))) {
      return 'Please enter your last name.';
    }
    if (message.contains('name should contain only letters')) {
      return 'Names should only contain letters and spaces.';
    }

    // Network and server errors
    if (message.contains('network') || message.contains('connection')) {
      return 'Please check your internet connection and try again.';
    }
    if (message.contains('server error') || message.contains('internal error')) {
      return 'Something went wrong on our end. Please try again in a few minutes.';
    }
    if (message.contains('timeout')) {
      return 'The request took too long. Please check your connection and try again.';
    }

    // Account verification errors
    if (message.contains('account not verified') || message.contains('verification pending')) {
      return 'Please check your email and verify your account before signing in.';
    }

    // Generic validation errors
    if (message.contains('required field') || message.contains('field is required')) {
      return 'Please fill in all required fields.';
    }
    if (message.contains('invalid json') || message.contains('malformed request')) {
      return 'There was a problem with your request. Please try again.';
    }

    // Rate limiting
    if (message.contains('too many requests') || message.contains('rate limit')) {
      return 'Too many attempts. Please wait a few minutes before trying again.';
    }

    // If no specific mapping found, return a cleaned up version of the original message
    // Capitalize first letter and ensure it ends with a period
    String cleanMessage = apiErrorMessage.trim();
    if (cleanMessage.isNotEmpty) {
      cleanMessage = cleanMessage[0].toUpperCase() + cleanMessage.substring(1);
      if (!cleanMessage.endsWith('.') && !cleanMessage.endsWith('!') && !cleanMessage.endsWith('?')) {
        cleanMessage += '.';
      }
    }

    return cleanMessage.isNotEmpty ? cleanMessage : 'Something went wrong. Please try again.';
  }

  Future<void> _handleSignUp() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_agreeToTerms) {
      Toast.error('Please agree to the terms of service and privacy policy');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create sign-up request
      final signUpRequest = SignUpRequest(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        repeatedPassword: _confirmPasswordController.text,
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
      );

      // Call the authentication service
      final response = await AuthService.signUp(signUpRequest);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.success) {
          // Show success message
          Toast.success(response.message);

          // Navigate to verification screen
          _navigateToVerification();
        } else {
          // Convert API error message to user-friendly message
          final userFriendlyMessage = _getUserFriendlyErrorMessage(response.message);
          Toast.error(userFriendlyMessage);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        Toast.error('An unexpected error occurred. Please try again.');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                         MediaQuery.of(context).padding.top -
                         MediaQuery.of(context).padding.bottom,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: _getHorizontalPadding(context),
                vertical: _getVerticalPadding(context),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: _getSpacing(context, 40)),

                  // Welcome text
                  _buildWelcomeText(context),

                  SizedBox(height: _getSpacing(context, 32)),

                  // Sign up form
                  _buildSignUpForm(context),

                  SizedBox(height: _getSpacing(context, 24)),

                  // Terms and conditions
                  _buildTermsAndConditions(context),

                  SizedBox(height: _getSpacing(context, 32)),

                  // Sign up button
                  _buildSignUpButton(context),

                  SizedBox(height: _getSpacing(context, 24)),

                  // Or divider
                  _buildOrDivider(context),

                  SizedBox(height: _getSpacing(context, 24)),

                  // Sign in link
                  _buildSignInLink(context),

                  SizedBox(height: _getSpacing(context, 20)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeText(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: 'Hello ',
                style: TextStyle(
                  fontSize: _getHeadingFontSize(context),
                  fontWeight: FontWeight.w700,
                  color: AppColors.black,
                  fontFamily: 'Inter',
                ),
              ),
              TextSpan(
                text: 'There',
                style: TextStyle(
                  fontSize: _getHeadingFontSize(context),
                  fontWeight: FontWeight.w700,
                  color: AppColors.primary,
                  fontFamily: 'Inter',
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: _getSpacing(context, 8)),
        Text(
          'Please fill in the details below to get started',
          style: TextStyle(
            fontSize: _getBodyFontSize(context),
            fontWeight: FontWeight.w400,
            color: AppColors.black.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // First Name and Last Name Row
          Row(
            children: [
              Expanded(
                child: _buildTextFormField(
                  controller: _firstNameController,
                  labelText: 'First Name',
                  hintText: 'Enter your first name',
                  prefixIcon: Icons.person_outline,
                  keyboardType: TextInputType.name,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your first name';
                    }
                    if (value.length < 2) {
                      return 'First name must be at least 2 characters';
                    }
                    return null;
                  },
                ),
              ),
              SizedBox(width: _getSpacing(context, 12)),
              Expanded(
                child: _buildTextFormField(
                  controller: _lastNameController,
                  labelText: 'Last Name',
                  hintText: 'Enter your last name',
                  prefixIcon: Icons.person_outline,
                  keyboardType: TextInputType.name,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your last name';
                    }
                    if (value.length < 2) {
                      return 'Last name must be at least 2 characters';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Email Field
          _buildTextFormField(
            controller: _emailController,
            labelText: 'Email',
            hintText: 'Enter your email address',
            prefixIcon: Icons.email_outlined,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email address';
              }
              if (!AuthService.isValidEmail(value)) {
                return 'Please enter a valid email address';
              }
              return null;
            },
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Phone Number Field
          _buildTextFormField(
            controller: _phoneController,
            labelText: 'Phone Number',
            hintText: 'Enter your phone number',
            prefixIcon: Icons.phone_outlined,
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your phone number';
              }
              if (!AuthService.isValidPhoneNumber(value)) {
                return 'Please enter a valid phone number';
              }
              return null;
            },
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Password Field
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTextFormField(
                controller: _passwordController,
                labelText: 'Password',
                hintText: 'Enter your password',
                prefixIcon: Icons.lock_outline,
                obscureText: !_isPasswordVisible,
                suffixIcon: IconButton(
                  onPressed: _togglePasswordVisibility,
                  icon: Icon(
                    _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your password';
                  }
                  if (value.length < 8) {
                    return 'Password must be at least 8 characters long';
                  }
                  if (!RegExp(r'[A-Z]').hasMatch(value)) {
                    return 'Password needs at least one uppercase letter';
                  }
                  if (!RegExp(r'[a-z]').hasMatch(value)) {
                    return 'Password needs at least one lowercase letter';
                  }
                  if (!RegExp(r'[0-9]').hasMatch(value)) {
                    return 'Password needs at least one number';
                  }
                  if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
                    return 'Password needs at least one special character (@, #, \$, etc.)';
                  }
                  return null;
                },
              ),
              SizedBox(height: _getSpacing(context, 8)),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: _getSpacing(context, 4)),
                child: Text(
                  'Password must contain: 8+ characters, uppercase, lowercase, number, and special character',
                  style: TextStyle(
                    fontSize: _getSmallFontSize(context) * 0.9,
                    color: AppColors.black.withValues(alpha: 0.5),
                    fontWeight: FontWeight.w400,
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Confirm Password Field
          _buildTextFormField(
            controller: _confirmPasswordController,
            labelText: 'Confirm Password',
            hintText: 'Confirm your password',
            prefixIcon: Icons.lock_outline,
            obscureText: !_isConfirmPasswordVisible,
            suffixIcon: IconButton(
              onPressed: _toggleConfirmPasswordVisibility,
              icon: Icon(
                _isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility,
                color: AppColors.black.withValues(alpha: 0.6),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please confirm your password';
              }
              if (value != _passwordController.text) {
                return 'Passwords do not match';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required IconData prefixIcon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      validator: validator,
      style: TextStyle(
        fontSize: _getBodyFontSize(context),
        color: AppColors.black,
      ),
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: Icon(
          prefixIcon,
          color: AppColors.black.withValues(alpha: 0.6),
          size: _getIconSize(context),
        ),
        suffixIcon: suffixIcon,
        labelStyle: TextStyle(
          fontSize: _getBodyFontSize(context),
          color: AppColors.black.withValues(alpha: 0.6),
        ),
        hintStyle: TextStyle(
          fontSize: _getBodyFontSize(context),
          color: AppColors.black.withValues(alpha: 0.4),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: BorderSide(
            color: AppColors.black.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: BorderSide(
            color: AppColors.black.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: const BorderSide(
            color: AppColors.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 2,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: _getSpacing(context, 16),
          vertical: _getSpacing(context, 16),
        ),
      ),
    );
  }

  Widget _buildTermsAndConditions(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: _toggleTermsAgreement,
          child: Container(
            width: _getCheckboxSize(context),
            height: _getCheckboxSize(context),
            decoration: BoxDecoration(
              color: _agreeToTerms ? AppColors.primary : AppColors.white,
              border: Border.all(
                color: _agreeToTerms
                    ? AppColors.primary
                    : AppColors.black.withValues(alpha: 0.3),
                width: _agreeToTerms ? 0 : 1,
              ),
              borderRadius: BorderRadius.circular(2),
            ),
            child: _agreeToTerms
                ? Icon(
                    Icons.check,
                    size: _getCheckboxIconSize(context),
                    color: AppColors.white,
                  )
                : null,
          ),
        ),
        SizedBox(width: _getSpacing(context, 12)),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: _getSmallFontSize(context),
                fontFamily: 'Inter',
                height: 1.5,
              ),
              children: [
                TextSpan(
                  text: 'By signing up',
                  style: TextStyle(
                    color: AppColors.black.withValues(alpha: 0.6),
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(
                  text: ', you agree to our ',
                  style: TextStyle(
                    color: AppColors.black.withValues(alpha: 0.6),
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(
                  text: 'terms of service',
                  style: const TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextSpan(
                  text: ' and ',
                  style: TextStyle(
                    color: AppColors.black.withValues(alpha: 0.6),
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(
                  text: 'privacy policy',
                  style: const TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: _getButtonHeight(context),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleSignUp,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? SizedBox(
                width: _getSpacing(context, 20),
                height: _getSpacing(context, 20),
                child: const CircularProgressIndicator(
                  color: AppColors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                'Sign up',
                style: TextStyle(
                  fontSize: _getBodyFontSize(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildOrDivider(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColors.black.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: _getSpacing(context, 16)),
          child: Text(
            'Or',
            style: TextStyle(
              fontSize: _getSmallFontSize(context),
              color: AppColors.black.withValues(alpha: 0.6),
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColors.black.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignInLink(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: _navigateToSignIn,
        child: RichText(
          text: TextSpan(
            style: TextStyle(
              fontSize: _getSmallFontSize(context),
              fontFamily: 'Inter',
            ),
            children: [
              TextSpan(
                text: 'Already have an account? ',
                style: TextStyle(
                  color: AppColors.black.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w400,
                ),
              ),
              const TextSpan(
                text: 'Sign in',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getVerticalPadding(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight < 600) return 12; // Short screens
    if (screenHeight > 800) return 24; // Tall screens
    return 16; // Normal screens
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getHeadingFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 20; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 32; // Tablet
    } else {
      baseSize = 26; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.85;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getSmallFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 14; // Tablet
    } else {
      baseSize = 12; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 18; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 24; // Tablet
    } else {
      baseSize = 20; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 8; // Smartwatch
    if (screenWidth > 600) return 16; // Tablet
    return 12; // Mobile
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 40; // Smartwatch
    } else if (screenWidth > 600) {
      baseHeight = 56; // Tablet
    } else {
      baseHeight = 48; // Mobile
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }

  double _getCheckboxSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 16; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 20; // Tablet
    } else {
      baseSize = 18; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getCheckboxIconSize(BuildContext context) {
    final checkboxSize = _getCheckboxSize(context);
    return checkboxSize * 0.7; // Icon is 70% of checkbox size
  }
}