import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing package delivery data in local storage
class PackageDataService {
  static const String _senderDataKey = 'sender_data';
  static const String _pickupDataKey = 'pickup_data';
  static const String _receiverDataKey = 'receiver_data';
  static const String _packageDataKey = 'package_data';
  static const String _pickupLocationHistoryKey = 'pickup_location_history';
  static const String _receiverLocationHistoryKey = 'receiver_location_history';
  static const String _completedOrdersKey = 'completed_orders';
  static const String _currentShipmentsKey = 'current_shipments';

  /// Save sender details
  static Future<void> saveSenderData(Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_senderDataKey, jsonEncode(data));
  }

  /// Get sender details
  static Future<Map<String, dynamic>?> getSenderData() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(_senderDataKey);
    if (data != null) {
      return jsonDecode(data);
    }
    return null;
  }

  /// Save pickup details
  static Future<void> savePickupData(Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_pickupDataKey, jsonEncode(data));
  }

  /// Get pickup details
  static Future<Map<String, dynamic>?> getPickupData() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(_pickupDataKey);
    if (data != null) {
      return jsonDecode(data);
    }
    return null;
  }

  /// Save receiver details
  static Future<void> saveReceiverData(Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_receiverDataKey, jsonEncode(data));
  }

  /// Get receiver details
  static Future<Map<String, dynamic>?> getReceiverData() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(_receiverDataKey);
    if (data != null) {
      return jsonDecode(data);
    }
    return null;
  }

  /// Save package details
  static Future<void> savePackageData(Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_packageDataKey, jsonEncode(data));
  }

  /// Get package details
  static Future<Map<String, dynamic>?> getPackageData() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(_packageDataKey);
    if (data != null) {
      return jsonDecode(data);
    }
    return null;
  }

  /// Clear all package data
  static Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_senderDataKey);
    await prefs.remove(_pickupDataKey);
    await prefs.remove(_receiverDataKey);
    await prefs.remove(_packageDataKey);
  }

  /// Save completed order/shipment
  static Future<void> saveCompletedOrder(Map<String, dynamic> orderData) async {
    final prefs = await SharedPreferences.getInstance();
    final ordersJson = prefs.getString(_completedOrdersKey);
    List<Map<String, dynamic>> orders = [];

    if (ordersJson != null) {
      final ordersList = jsonDecode(ordersJson) as List;
      orders = ordersList.cast<Map<String, dynamic>>();
    }

    // Add new order to the beginning (most recent first)
    orders.insert(0, {
      ...orderData,
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'status': 'pending', // Initial status
    });

    // Keep only the last 50 orders to prevent storage bloat
    if (orders.length > 50) {
      orders = orders.take(50).toList();
    }

    await prefs.setString(_completedOrdersKey, jsonEncode(orders));
  }

  /// Get completed orders/shipments
  static Future<List<Map<String, dynamic>>> getCompletedOrders() async {
    final prefs = await SharedPreferences.getInstance();
    final ordersJson = prefs.getString(_completedOrdersKey);

    if (ordersJson != null) {
      final ordersList = jsonDecode(ordersJson) as List;
      return ordersList.cast<Map<String, dynamic>>();
    }
    return [];
  }

  /// Save current shipment
  static Future<void> saveCurrentShipment(Map<String, dynamic> shipmentData) async {
    final prefs = await SharedPreferences.getInstance();
    final shipmentsJson = prefs.getString(_currentShipmentsKey);
    List<Map<String, dynamic>> shipments = [];

    if (shipmentsJson != null) {
      final shipmentsList = jsonDecode(shipmentsJson) as List;
      shipments = shipmentsList.cast<Map<String, dynamic>>();
    }

    // Add new shipment to the beginning (most recent first)
    shipments.insert(0, {
      ...shipmentData,
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    // Keep only the last 20 current shipments
    if (shipments.length > 20) {
      shipments = shipments.take(20).toList();
    }

    await prefs.setString(_currentShipmentsKey, jsonEncode(shipments));
  }

  /// Get current shipments
  static Future<List<Map<String, dynamic>>> getCurrentShipments() async {
    final prefs = await SharedPreferences.getInstance();
    final shipmentsJson = prefs.getString(_currentShipmentsKey);

    if (shipmentsJson != null) {
      final shipmentsList = jsonDecode(shipmentsJson) as List;
      return shipmentsList.cast<Map<String, dynamic>>();
    }
    return [];
  }

  /// Update shipment status
  static Future<void> updateShipmentStatus(String shipmentId, String newStatus) async {
    final prefs = await SharedPreferences.getInstance();

    // Update in current shipments
    final currentShipmentsJson = prefs.getString(_currentShipmentsKey);
    if (currentShipmentsJson != null) {
      final shipmentsList = jsonDecode(currentShipmentsJson) as List;
      final shipments = shipmentsList.cast<Map<String, dynamic>>();

      for (int i = 0; i < shipments.length; i++) {
        if (shipments[i]['id'] == shipmentId) {
          shipments[i]['status'] = newStatus;
          break;
        }
      }

      await prefs.setString(_currentShipmentsKey, jsonEncode(shipments));
    }

    // Update in completed orders
    final completedOrdersJson = prefs.getString(_completedOrdersKey);
    if (completedOrdersJson != null) {
      final ordersList = jsonDecode(completedOrdersJson) as List;
      final orders = ordersList.cast<Map<String, dynamic>>();

      for (int i = 0; i < orders.length; i++) {
        if (orders[i]['id'] == shipmentId) {
          orders[i]['status'] = newStatus;
          break;
        }
      }

      await prefs.setString(_completedOrdersKey, jsonEncode(orders));
    }
  }

  /// Clear completed orders
  static Future<void> clearCompletedOrders() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_completedOrdersKey);
  }

  /// Clear current shipments
  static Future<void> clearCurrentShipments() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentShipmentsKey);
  }

  /// Check if pickup details exist
  static Future<bool> hasPickupData() async {
    final data = await getPickupData();
    return data != null && data.isNotEmpty;
  }

  /// Check if receiver details exist
  static Future<bool> hasReceiverData() async {
    final data = await getReceiverData();
    return data != null && data.isNotEmpty;
  }

  /// Check if sender details exist
  static Future<bool> hasSenderData() async {
    final data = await getSenderData();
    return data != null && data.isNotEmpty;
  }

  /// Check if package details exist
  static Future<bool> hasPackageData() async {
    final data = await getPackageData();
    return data != null && data.isNotEmpty;
  }

  /// Save pickup location to history
  static Future<void> savePickupLocationToHistory(Map<String, dynamic> locationData) async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_pickupLocationHistoryKey);
    List<Map<String, dynamic>> history = [];

    if (historyJson != null) {
      final historyList = jsonDecode(historyJson) as List;
      history = historyList.cast<Map<String, dynamic>>();
    }

    // Remove duplicate if exists (based on address)
    history.removeWhere((item) => item['address'] == locationData['address']);

    // Add new location to the beginning
    history.insert(0, {
      ...locationData,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    // Keep only last 10 locations
    if (history.length > 10) {
      history = history.take(10).toList();
    }

    await prefs.setString(_pickupLocationHistoryKey, jsonEncode(history));
  }

  /// Get pickup location history
  static Future<List<Map<String, dynamic>>> getPickupLocationHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_pickupLocationHistoryKey);

    if (historyJson != null) {
      final historyList = jsonDecode(historyJson) as List;
      return historyList.cast<Map<String, dynamic>>();
    }

    return [];
  }

  /// Save receiver location to history
  static Future<void> saveReceiverLocationToHistory(Map<String, dynamic> locationData) async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_receiverLocationHistoryKey);
    List<Map<String, dynamic>> history = [];

    if (historyJson != null) {
      final historyList = jsonDecode(historyJson) as List;
      history = historyList.cast<Map<String, dynamic>>();
    }

    // Remove duplicate if exists (based on address)
    history.removeWhere((item) => item['address'] == locationData['address']);

    // Add new location to the beginning
    history.insert(0, {
      ...locationData,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    // Keep only last 10 locations
    if (history.length > 10) {
      history = history.take(10).toList();
    }

    await prefs.setString(_receiverLocationHistoryKey, jsonEncode(history));
  }

  /// Get receiver location history
  static Future<List<Map<String, dynamic>>> getReceiverLocationHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_receiverLocationHistoryKey);

    if (historyJson != null) {
      final historyList = jsonDecode(historyJson) as List;
      return historyList.cast<Map<String, dynamic>>();
    }

    return [];
  }
}
