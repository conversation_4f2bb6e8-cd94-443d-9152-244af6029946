import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/services/package_data_service.dart';

/// Scheduled Delivery form widget
///
/// This widget contains the form fields specific to scheduled delivery.
/// It's designed to be used within a tabbed interface.
class ScheduledDeliveryWidget extends StatefulWidget {
  final VoidCallback? onAddToCargo;
  final VoidCallback? onAddPickupDetails;
  final VoidCallback? onAddReceiverDetails;
  final Function(Map<String, dynamic>)? onDataChanged;
  final bool hasPickupDetails;
  final bool hasReceiverDetails;

  const ScheduledDeliveryWidget({
    super.key,
    this.onAddToCargo,
    this.onAddPickupDetails,
    this.onAddReceiverDetails,
    this.onDataChanged,
    this.hasPickupDetails = false,
    this.hasReceiverDetails = false,
  });

  @override
  State<ScheduledDeliveryWidget> createState() => _ScheduledDeliveryWidgetState();
}

class _ScheduledDeliveryWidgetState extends State<ScheduledDeliveryWidget> {
  DateTime? _selectedPickupDate;
  DateTime? _selectedDeliveryDate;
  TimeOfDay? _selectedPickupTime;
  TimeOfDay? _selectedDeliveryTime;

  bool _hasPickupData = false;
  bool _hasReceiverData = false;

  // Data for preview
  Map<String, dynamic>? _pickupData;
  Map<String, dynamic>? _receiverData;

  @override
  void initState() {
    super.initState();
    _checkSavedData();
  }

  @override
  void didUpdateWidget(ScheduledDeliveryWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Refresh data when parent state changes
    if (oldWidget.hasPickupDetails != widget.hasPickupDetails ||
        oldWidget.hasReceiverDetails != widget.hasReceiverDetails) {
      _checkSavedData();
    }
  }

  /// Check if saved data exists for pickup and receiver details
  Future<void> _checkSavedData() async {
    final hasPickup = await PackageDataService.hasPickupData();
    final hasReceiver = await PackageDataService.hasReceiverData();

    // Get the actual data for preview
    Map<String, dynamic>? pickupData;
    Map<String, dynamic>? receiverData;

    if (hasPickup) {
      pickupData = await PackageDataService.getPickupData();
    }

    if (hasReceiver) {
      receiverData = await PackageDataService.getReceiverData();
    }

    setState(() {
      _hasPickupData = hasPickup;
      _hasReceiverData = hasReceiver;
      _pickupData = pickupData;
      _receiverData = receiverData;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20), // Increased padding
        child: Column(
          children: [
            SizedBox(height: 32), // Increased spacing

            // Info banner
            _buildInfoBanner(context),

            SizedBox(height: 28), // Increased spacing

            // Schedule pickup date and time
            _buildSchedulePickupSection(context),

            SizedBox(height: 20), // Reduced spacing

            // Schedule pickup time
            _buildSchedulePickupTimeSection(context),

            SizedBox(height: 28), // Increased spacing

            // Schedule delivery date and time
            _buildScheduleDeliverySection(context),

            SizedBox(height: 20), // Reduced spacing

            // Schedule delivery time
            _buildScheduleDeliveryTimeSection(context),

            SizedBox(height: 32), // Increased spacing

            // Add pickup details
            _buildPickupDetailsSection(context),

            SizedBox(height: 32), // Increased spacing

            // Add receiver details
            _buildReceiverDetailsSection(context),

            SizedBox(height: 50), // Increased bottom spacing
          ],
        ),
      ),
    );
  }



  Widget _buildInfoBanner(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 19),
      padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 12,
            color: const Color(0xFF8638E5),
          ),
          SizedBox(width: 7),
          Expanded(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: 'Schedule delivery with precise date and time selection is cheaper and guaranteed, ',
                    style: TextStyle(
                      color: const Color(0xFF4E4855),
                      fontSize: 10,
                      fontFamily: 'Lato',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  TextSpan(
                    text: ' Read more',
                    style: TextStyle(
                      color: const Color(0xFF8638E5),
                      fontSize: 10,
                      fontFamily: 'Lato',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }





  Widget _buildSchedulePickupSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20), // Increased margin
      width: double.infinity,
      padding: const EdgeInsets.all(24), // Increased padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20), // Increased border radius
        border: Border.all(
          color: const Color(0x1A1E1E1E), // More visible border
          width: 1.5, // Thicker border
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08), // More prominent shadow
            blurRadius: 12, // Increased blur
            offset: const Offset(0, 4), // Increased offset
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Schedule pickup date',
                  style: TextStyle(
                    color: Colors.black.withValues(alpha: 0.6), // Darker text
                    fontSize: 14, // Increased font size
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600, // Bolder weight
                    height: 1.4,
                  ),
                ),
                if (_selectedPickupDate != null) ...[
                  const SizedBox(height: 8), // Increased spacing
                  Text(
                    '${_selectedPickupDate!.day}/${_selectedPickupDate!.month}/${_selectedPickupDate!.year}',
                    style: TextStyle(
                      color: Colors.black.withValues(alpha: 0.9), // Darker text
                      fontSize: 16, // Increased font size
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600, // Bolder weight
                    ),
                  ),
                ],
              ],
            ),
          ),
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _selectPickupDate(context),
              borderRadius: BorderRadius.circular(12), // Increased border radius
              child: Container(
                width: 48, // Increased size
                height: 48, // Increased size
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1), // More visible background
                  borderRadius: BorderRadius.circular(12), // Increased border radius
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.calendar_today,
                  size: 22, // Increased icon size
                  color: AppColors.primary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSchedulePickupTimeSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20), // Increased margin
      width: double.infinity,
      padding: const EdgeInsets.all(24), // Increased padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20), // Increased border radius
        border: Border.all(
          color: const Color(0x1A1E1E1E), // More visible border
          width: 1.5, // Thicker border
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08), // More prominent shadow
            blurRadius: 12, // Increased blur
            offset: const Offset(0, 4), // Increased offset
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Schedule pickup time',
                  style: TextStyle(
                    color: Colors.black.withValues(alpha: 0.6), // Darker text
                    fontSize: 14, // Increased font size
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600, // Bolder weight
                    height: 1.4,
                  ),
                ),
                if (_selectedPickupTime != null) ...[
                  const SizedBox(height: 8), // Increased spacing
                  Text(
                    _selectedPickupTime!.format(context),
                    style: TextStyle(
                      color: Colors.black.withValues(alpha: 0.9), // Darker text
                      fontSize: 16, // Increased font size
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600, // Bolder weight
                    ),
                  ),
                ],
              ],
            ),
          ),
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _selectPickupTime(context),
              borderRadius: BorderRadius.circular(12), // Increased border radius
              child: Container(
                width: 48, // Increased size
                height: 48, // Increased size
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1), // More visible background
                  borderRadius: BorderRadius.circular(12), // Increased border radius
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.access_time,
                  size: 22, // Increased icon size
                  color: AppColors.primary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleDeliverySection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20), // Increased margin
      width: double.infinity,
      padding: const EdgeInsets.all(24), // Increased padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20), // Increased border radius
        border: Border.all(
          color: const Color(0x1A1E1E1E), // More visible border
          width: 1.5, // Thicker border
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08), // More prominent shadow
            blurRadius: 12, // Increased blur
            offset: const Offset(0, 4), // Increased offset
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Schedule Delivery date',
                  style: TextStyle(
                    color: Colors.black.withValues(alpha: 0.6), // Darker text
                    fontSize: 14, // Increased font size
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600, // Bolder weight
                    height: 1.4,
                  ),
                ),
                if (_selectedDeliveryDate != null) ...[
                  const SizedBox(height: 8), // Increased spacing
                  Text(
                    '${_selectedDeliveryDate!.day}/${_selectedDeliveryDate!.month}/${_selectedDeliveryDate!.year}',
                    style: TextStyle(
                      color: Colors.black.withValues(alpha: 0.9), // Darker text
                      fontSize: 16, // Increased font size
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600, // Bolder weight
                    ),
                  ),
                ],
              ],
            ),
          ),
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _selectDeliveryDate(context),
              borderRadius: BorderRadius.circular(12), // Increased border radius
              child: Container(
                width: 48, // Increased size
                height: 48, // Increased size
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1), // More visible background
                  borderRadius: BorderRadius.circular(12), // Increased border radius
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.calendar_today,
                  size: 22, // Increased icon size
                  color: AppColors.primary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleDeliveryTimeSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20), // Increased margin
      width: double.infinity,
      padding: const EdgeInsets.all(24), // Increased padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20), // Increased border radius
        border: Border.all(
          color: const Color(0x1A1E1E1E), // More visible border
          width: 1.5, // Thicker border
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08), // More prominent shadow
            blurRadius: 12, // Increased blur
            offset: const Offset(0, 4), // Increased offset
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Schedule delivery time',
                  style: TextStyle(
                    color: Colors.black.withValues(alpha: 0.6), // Darker text
                    fontSize: 14, // Increased font size
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600, // Bolder weight
                    height: 1.4,
                  ),
                ),
                if (_selectedDeliveryTime != null) ...[
                  const SizedBox(height: 8), // Increased spacing
                  Text(
                    _selectedDeliveryTime!.format(context),
                    style: TextStyle(
                      color: Colors.black.withValues(alpha: 0.9), // Darker text
                      fontSize: 16, // Increased font size
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600, // Bolder weight
                    ),
                  ),
                ],
              ],
            ),
          ),
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _selectDeliveryTime(context),
              borderRadius: BorderRadius.circular(12), // Increased border radius
              child: Container(
                width: 48, // Increased size
                height: 48, // Increased size
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1), // More visible background
                  borderRadius: BorderRadius.circular(12), // Increased border radius
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.access_time,
                  size: 22, // Increased icon size
                  color: AppColors.primary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPickupDetailsSection(BuildContext context) {
    // Use widget parameter for hasData, but show preview if we have internal data
    final hasData = widget.hasPickupDetails || _hasPickupData;
    return _buildDetailsSection(
      context,
      title: hasData ? 'Edit pickup details' : 'Add pickup details',
      hasData: hasData,
      previewData: _pickupData,
      onAddPressed: () async {
        if (widget.onAddPickupDetails != null) {
          widget.onAddPickupDetails!();
          // Refresh data status after returning from pickup details
          await _checkSavedData();
        }
      },
    );
  }

  Widget _buildReceiverDetailsSection(BuildContext context) {
    // Use widget parameter for hasData, but show preview if we have internal data
    final hasData = widget.hasReceiverDetails || _hasReceiverData;
    return _buildDetailsSection(
      context,
      title: hasData ? 'Edit receiver details' : 'Add receiver details',
      hasData: hasData,
      previewData: _receiverData,
      onAddPressed: () async {
        if (widget.onAddReceiverDetails != null) {
          widget.onAddReceiverDetails!();
          // Refresh data status after returning from receiver details
          await _checkSavedData();
        }
      },
    );
  }



  Widget _buildDetailsSection(
    BuildContext context, {
    required String title,
    required VoidCallback onAddPressed,
    required bool hasData,
    Map<String, dynamic>? previewData,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20), // Increased margin
      width: double.infinity,
      padding: const EdgeInsets.all(24), // Increased padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20), // Increased border radius
        border: Border.all(
          color: const Color(0x1A1E1E1E), // More visible border
          width: 1.5, // Thicker border
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08), // More prominent shadow
            blurRadius: 12, // Increased blur
            offset: const Offset(0, 4), // Increased offset
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: const Color(0xE6000000), // Darker text
                    fontSize: 16, // Increased font size
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600, // Bolder weight
                    height: 1.4,
                  ),
                ),
              ),
              const SizedBox(width: 16), // Increased spacing
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onAddPressed,
                  borderRadius: BorderRadius.circular(16), // Increased border radius
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 14), // Increased padding
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(16), // Increased border radius
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withValues(alpha: 0.4), // More prominent shadow
                          blurRadius: 12, // Increased blur
                          offset: const Offset(0, 4), // Increased offset
                        ),
                      ],
                    ),
                    child: Text(
                      hasData ? 'Edit' : 'Add',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14, // Increased font size
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600, // Bolder weight
                        height: 1.2,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),

          // Preview data when available
          if (hasData && previewData != null) ...[
            const SizedBox(height: 16), // Increased spacing
            _buildPreviewData(previewData),
          ],
        ],
      ),
    );
  }

  Widget _buildPreviewData(Map<String, dynamic> data) {
    return Container(
      padding: const EdgeInsets.all(16), // Increased padding
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.08), // Slightly more visible background
        borderRadius: BorderRadius.circular(12), // Increased border radius
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.15), // More visible border
          width: 1.5, // Thicker border
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (data['name'] != null || data['senderName'] != null) ...[
            _buildPreviewItem(
              'Name',
              data['name'] ?? data['senderName'] ?? '',
              Icons.person_outline,
            ),
          ],
          if (data['state'] != null) ...[
            const SizedBox(height: 12), // Increased spacing
            _buildPreviewItem(
              'State',
              data['state'] ?? '',
              Icons.location_city_outlined,
            ),
          ],
          if (data['address'] != null || data['fullAddress'] != null) ...[
            const SizedBox(height: 12), // Increased spacing
            _buildPreviewItem(
              'Location',
              data['address'] ?? data['fullAddress'] ?? '',
              Icons.location_on_outlined,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPreviewItem(String label, String value, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 20, // Increased icon size
          color: AppColors.primary.withValues(alpha: 0.8), // More visible icon
        ),
        const SizedBox(width: 12), // Increased spacing
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 13, // Increased font size
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600, // Bolder weight
                  color: AppColors.primary.withValues(alpha: 0.9), // More visible
                ),
              ),
              const SizedBox(height: 4), // Increased spacing
              Text(
                value,
                style: TextStyle(
                  fontSize: 14, // Increased font size
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500, // Slightly bolder
                  color: Colors.black.withValues(alpha: 0.85), // Darker text
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _selectPickupDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedPickupDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedPickupDate) {
      setState(() {
        _selectedPickupDate = picked;
      });
      _notifyDataChanged();
    }
  }

  Future<void> _selectDeliveryDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDeliveryDate ?? (_selectedPickupDate ?? DateTime.now()),
      firstDate: _selectedPickupDate ?? DateTime.now(),
      lastDate: DateTime.now().add(Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDeliveryDate) {
      setState(() {
        _selectedDeliveryDate = picked;
      });
      _notifyDataChanged();
    }
  }

  Future<void> _selectPickupTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedPickupTime ?? TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedPickupTime) {
      setState(() {
        _selectedPickupTime = picked;
      });
      _notifyDataChanged();
    }
  }

  Future<void> _selectDeliveryTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedDeliveryTime ?? (_selectedPickupTime ?? TimeOfDay.now()),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDeliveryTime) {
      setState(() {
        _selectedDeliveryTime = picked;
      });
      _notifyDataChanged();
    }
  }

  /// Notify parent widget about data changes
  void _notifyDataChanged() {
    if (widget.onDataChanged != null) {
      final data = {
        'itemName': 'Scheduled Package',
        'category': 'General', // Default category
        'itemType': 'Scheduled Package',
        'deliveryType': 'scheduled',
        'pickupDate': _selectedPickupDate?.toIso8601String(),
        'deliveryDate': _selectedDeliveryDate?.toIso8601String(),
        'pickupTime': _selectedPickupTime != null ? '${_selectedPickupTime!.hour}:${_selectedPickupTime!.minute.toString().padLeft(2, '0')}' : null,
        'deliveryTime': _selectedDeliveryTime != null ? '${_selectedDeliveryTime!.hour}:${_selectedDeliveryTime!.minute.toString().padLeft(2, '0')}' : null,
        'hasPickupData': _hasPickupData,
        'hasReceiverData': _hasReceiverData,
        'pickupData': _pickupData,
        'receiverData': _receiverData,
      };
      widget.onDataChanged!(data);
    }
  }
}