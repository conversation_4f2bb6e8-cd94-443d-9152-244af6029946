import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/services/address_service.dart';
import 'package:rideoon/views/custom_widgets/push_notfication.dart';

/// Address Management view for user account
class AddressView extends StatefulWidget {
  const AddressView({super.key});

  @override
  State<AddressView> createState() => _AddressViewState();
}

class _AddressViewState extends State<AddressView> {
  List<Map<String, dynamic>> _addresses = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSavedAddresses();
  }

  /// Load saved addresses from AddressService
  Future<void> _loadSavedAddresses() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final addresses = await AddressService.getSavedAddresses();

      setState(() {
        _addresses = addresses.map((addr) => {
          ...addr,
          'icon': _getAddressIcon(addr['type']),
          'title': _getAddressTitle(addr),
        }).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _addresses = [];
        _isLoading = false;
      });
      Toast.error('Failed to load addresses: $e');
    }
  }

  /// Get appropriate icon for address type
  IconData _getAddressIcon(String? type) {
    switch (type) {
      case 'pickup':
        return Icons.send;
      case 'receiver':
        return Icons.location_on;
      default:
        return Icons.place;
    }
  }

  /// Get display title for address
  String _getAddressTitle(Map<String, dynamic> address) {
    if (address['type'] == 'pickup') {
      return address['senderName'] ?? 'Pickup Address';
    } else if (address['type'] == 'receiver') {
      return address['name'] ?? 'Delivery Address';
    }
    return 'Address';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: Column(
          children: [
            // Add new address button
            Padding(
              padding: EdgeInsets.all(_getHorizontalPadding(context)),
              child: _buildAddNewAddressButton(context),
            ),

            SizedBox(height: _getSpacing(context, 16)),

            // Addresses list
            Expanded(
              child: _isLoading
                  ? _buildLoadingState(context)
                  : _buildAddressesList(context),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFF5F5FF),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.black,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'Address Management',
        style: TextStyle(
          fontSize: _getFontSize(context, 20),
          fontWeight: FontWeight.bold,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
    );
  }

  Widget _buildAddNewAddressButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () {
          _showAddAddressDialog(context);
        },
        icon: Icon(
          Icons.add,
          size: _getIconSize(context, 20),
          color: AppColors.white,
        ),
        label: Text(
          'Add New Address',
          style: TextStyle(
            fontSize: _getFontSize(context, 16),
            fontWeight: FontWeight.w600,
            fontFamily: 'Poppins',
            color: AppColors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          padding: EdgeInsets.symmetric(
            vertical: _getSpacing(context, 16),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildAddressesList(BuildContext context) {
    if (_addresses.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      itemCount: _addresses.length,
      itemBuilder: (context, index) {
        final address = _addresses[index];
        return Padding(
          padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
          child: _buildAddressCard(context, address, index),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 64,
            color: AppColors.black.withValues(alpha: 0.3),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            'No Saved Addresses',
            style: TextStyle(
              fontSize: _getFontSize(context, 18),
              fontWeight: FontWeight.w600,
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            'Add addresses from pickup or delivery forms\nto see them here',
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAddressCard(BuildContext context, Map<String, dynamic> address, int index) {
    final isDefault = address['isDefault'] ?? false;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: isDefault 
              ? AppColors.primary.withValues(alpha: 0.3)
              : AppColors.black.withValues(alpha: 0.1),
          width: isDefault ? 2 : 1,
        ),
        boxShadow: isDefault
            ? [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  blurRadius: _getSpacing(context, 8),
                  offset: Offset(0, _getSpacing(context, 2)),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and default badge
          Row(
            children: [
              Container(
                width: _getIconSize(context, 40),
                height: _getIconSize(context, 40),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                ),
                child: Icon(
                  address['icon'] as IconData,
                  size: _getIconSize(context, 20),
                  color: AppColors.primary,
                ),
              ),
              SizedBox(width: _getSpacing(context, 12)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          address['title'] as String,
                          style: TextStyle(
                            fontSize: _getFontSize(context, 16),
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Poppins',
                            color: AppColors.black,
                          ),
                        ),
                        if (isDefault) ...[
                          SizedBox(width: _getSpacing(context, 8)),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: _getSpacing(context, 8),
                              vertical: _getSpacing(context, 4),
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                            ),
                            child: Text(
                              'Default',
                              style: TextStyle(
                                fontSize: _getFontSize(context, 10),
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Poppins',
                                color: AppColors.white,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              // More options button
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  size: _getIconSize(context, 20),
                  color: AppColors.black.withValues(alpha: 0.6),
                ),
                onSelected: (value) {
                  _handleAddressAction(context, value, address, index);
                },
                itemBuilder: (context) => [
                  if (!isDefault)
                    PopupMenuItem(
                      value: 'set_default',
                      child: Row(
                        children: [
                          Icon(Icons.star_outline, size: _getIconSize(context, 16)),
                          SizedBox(width: _getSpacing(context, 8)),
                          Text('Set as Default'),
                        ],
                      ),
                    ),
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit_outlined, size: _getIconSize(context, 16)),
                        SizedBox(width: _getSpacing(context, 8)),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  if (!isDefault)
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete_outline, size: _getIconSize(context, 16), color: AppColors.error),
                          SizedBox(width: _getSpacing(context, 8)),
                          Text('Delete', style: TextStyle(color: AppColors.error)),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 12)),

          // Address details
          Text(
            _getDisplayAddress(address),
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.8),
            ),
          ),
          SizedBox(height: _getSpacing(context, 4)),
          Text(
            _getDisplayLocation(address),
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.6),
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          // Address type badge
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 8),
              vertical: _getSpacing(context, 4),
            ),
            decoration: BoxDecoration(
              color: address['type'] == 'pickup'
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
            ),
            child: Text(
              address['type'] == 'pickup' ? 'Pickup Address' : 'Delivery Address',
              style: TextStyle(
                fontSize: _getFontSize(context, 12),
                fontWeight: FontWeight.w500,
                fontFamily: 'Poppins',
                color: address['type'] == 'pickup'
                    ? AppColors.primary
                    : AppColors.success,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get display address text
  String _getDisplayAddress(Map<String, dynamic> address) {
    if (address['type'] == 'pickup') {
      return address['fullAddress'] ?? address['address'] ?? 'No address';
    } else {
      return address['address'] ?? 'No address';
    }
  }

  /// Get display location text
  String _getDisplayLocation(Map<String, dynamic> address) {
    final state = address['state'] ?? '';
    final phone = address['phone'] ?? '';

    if (state.isNotEmpty && phone.isNotEmpty) {
      return '$state • $phone';
    } else if (state.isNotEmpty) {
      return state;
    } else if (phone.isNotEmpty) {
      return phone;
    }
    return 'No location info';
  }

  void _handleAddressAction(BuildContext context, String action, Map<String, dynamic> address, int index) {
    switch (action) {
      case 'set_default':
        // Note: Default functionality can be implemented later if needed
        Toast.info('Default address feature coming soon');
        break;
      case 'edit':
        _showEditAddressDialog(context, address, index);
        break;
      case 'delete':
        _showDeleteConfirmation(context, address, index);
        break;
    }
  }

  void _showAddAddressDialog(BuildContext context) {
    Toast.info('Add address functionality to be implemented');
  }

  void _showEditAddressDialog(BuildContext context, Map<String, dynamic> address, int index) {
    Toast.info('Edit address functionality to be implemented');
  }

  void _showDeleteConfirmation(BuildContext context, Map<String, dynamic> address, int index) {
    PushNotificationDialog.show(
      context,
      title: 'Delete Address',
      description: 'Are you sure you want to delete "${address['title']}" address? This action cannot be undone.',
      acceptButtonText: 'Delete',
      declineButtonText: 'Cancel',
      icon: Icons.delete_outline,
      iconBackgroundColor: AppColors.error,
      iconColor: AppColors.error,
      onAccept: () async {
        try {
          await AddressService.deleteAddress(address['id']);
          await _loadSavedAddresses(); // Refresh the list
          Toast.success('Address deleted successfully');
        } catch (e) {
          Toast.error('Failed to delete address: $e');
        }
      },
      onDecline: () {
        // Dialog will close automatically
      },
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
