/// Request model for creating or updating an address
class AddressRequest {
  final String name;
  final int phoneNumber;
  final String street;
  final String city;
  final String state;
  final String country;
  final double? longitude;
  final double? latitude;

  const AddressRequest({
    required this.name,
    required this.phoneNumber,
    required this.street,
    required this.city,
    required this.state,
    required this.country,
    this.longitude,
    this.latitude,
  });

  /// Create AddressRequest from JSON
  factory AddressRequest.fromJson(Map<String, dynamic> json) {
    return AddressRequest(
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as int,
      street: json['street'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      country: json['country'] as String,
      longitude: json['longitude'] != null ? (json['longitude'] as num).toDouble() : null,
      latitude: json['latitude'] != null ? (json['latitude'] as num).toDouble() : null,
    );
  }

  /// Convert AddressRequest to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'name': name,
      'phoneNumber': phoneNumber,
      'street': street,
      'city': city,
      'state': state,
      'country': country,
    };

    if (longitude != null) {
      json['longitude'] = longitude;
    }

    if (latitude != null) {
      json['latitude'] = latitude;
    }

    return json;
  }

  /// Create a copy of this request with updated fields
  AddressRequest copyWith({
    String? name,
    int? phoneNumber,
    String? street,
    String? city,
    String? state,
    String? country,
    double? longitude,
    double? latitude,
  }) {
    return AddressRequest(
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      street: street ?? this.street,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      longitude: longitude ?? this.longitude,
      latitude: latitude ?? this.latitude,
    );
  }

  /// Validate the address request
  List<String> validate() {
    final errors = <String>[];

    if (name.trim().isEmpty) {
      errors.add('Name is required');
    }

    if (phoneNumber <= 0) {
      errors.add('Valid phone number is required');
    }

    if (street.trim().isEmpty) {
      errors.add('Street address is required');
    }

    if (city.trim().isEmpty) {
      errors.add('City is required');
    }

    if (state.trim().isEmpty) {
      errors.add('State is required');
    }

    if (country.trim().isEmpty) {
      errors.add('Country is required');
    }

    // Validate coordinates if provided
    if (longitude != null && (longitude! < -180 || longitude! > 180)) {
      errors.add('Longitude must be between -180 and 180');
    }

    if (latitude != null && (latitude! < -90 || latitude! > 90)) {
      errors.add('Latitude must be between -90 and 90');
    }

    return errors;
  }

  /// Check if the request is valid
  bool get isValid => validate().isEmpty;

  @override
  String toString() {
    return 'AddressRequest(name: $name, street: $street, city: $city, state: $state, country: $country)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AddressRequest &&
        other.name == name &&
        other.phoneNumber == phoneNumber &&
        other.street == street &&
        other.city == city &&
        other.state == state &&
        other.country == country &&
        other.longitude == longitude &&
        other.latitude == latitude;
  }

  @override
  int get hashCode {
    return Object.hash(
      name,
      phoneNumber,
      street,
      city,
      state,
      country,
      longitude,
      latitude,
    );
  }
}
